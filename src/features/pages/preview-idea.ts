import { customElement, state } from 'lit/decorators.js';
import { css } from 'lit';
import { html, SignalWatcher } from '@lit-labs/signals';
import { LitElement } from 'lit';

// Shoelace components
import '@shoelace-style/shoelace/dist/components/button/button.js';
import '@shoelace-style/shoelace/dist/components/icon/icon.js';

// Components
import '@layout/page-heading';
import '@components/idea/idea-preview-card';
import { IdeaPreviewData } from '@components/idea/idea-preview-card';

// State
import layout from '@state/layout';

// Utils
import { loadForm, formToJson } from '@components/common/saveable-form';

// Schemas
import ideaSchema from '@schemas/idea-schema.json';

// Icons
import arrowLeftIcon from '@shoelace-style/shoelace/dist/assets/icons/arrow-left.svg';
import rocketIcon from '@shoelace-style/shoelace/dist/assets/icons/rocket.svg';

@customElement('preview-idea')
export class PreviewIdea extends SignalWatcher(LitElement) {
  @state() private ideaData: IdeaPreviewData = {};
  @state() private hasFormData = false;

  static styles = css`
    :host {
      display: block;
      container-type: inline-size;
    }

    .container {
      max-width: 70rem;
      margin: 1.5rem 3rem;
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .preview-section {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .actions sl-button {
      min-width: 150px;
    }

    .no-data {
      text-align: center;
      padding: 3rem 1rem;
      color: var(--subtle-text);
    }

    .no-data h2 {
      color: var(--main-foreground);
      margin-bottom: 1rem;
    }

    .no-data p {
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    /* Responsive behavior for smaller screens */
    @container (width <= 768px) {
      .container {
        margin: 1rem;
      }

      .actions {
        flex-direction: column;
        align-items: center;
      }

      .actions sl-button {
        width: 100%;
        max-width: 300px;
      }
    }
  `;

  connectedCallback() {
    super.connectedCallback();
    layout.topBarContent.set(html`
      <page-heading>Preview Your Idea</page-heading>
    `);
    layout.showLeftSidebar.set(true);
    layout.showRightSidebar.set(false);
    layout.rightSidebarContent.set(html``);

    this.loadIdeaData();
  }

  private loadIdeaData() {
    try {
      // Load form data from localStorage
      const formData = loadForm('create-idea');
      
      if (formData) {
        // Convert form data to structured idea data using the schema
        const structuredData = formToJson('create-idea', ideaSchema);
        
        this.ideaData = {
          name: structuredData.name as string,
          description: structuredData.description as string,
          tags: structuredData.tags as string[],
        };
        
        this.hasFormData = true;
      } else {
        this.hasFormData = false;
      }
    } catch (error) {
      console.error('Error loading idea data:', error);
      this.hasFormData = false;
    }
  }

  private handleBackToEdit() {
    // Navigate back to the create idea form
    window.history.back();
  }

  private handleSubmitIdea() {
    // Navigate to create idea page and trigger submission
    // We'll use a URL parameter to indicate we want to submit immediately
    window.location.href = '/create-idea?submit=true';
  }

  render() {
    if (!this.hasFormData) {
      return html`
        <div class="container">
          <div class="no-data">
            <h2>No Idea Data Found</h2>
            <p>
              It looks like you haven't started creating an idea yet, or your form data has expired.
              Please go back to the create idea form to get started.
            </p>
            <sl-button variant="primary" href="/create-idea">
              <sl-icon slot="prefix" src="${arrowLeftIcon}"></sl-icon>
              Create New Idea
            </sl-button>
          </div>
        </div>
      `;
    }

    return html`
      <div class="container">
        <div class="preview-section">
          <idea-preview-card .ideaData=${this.ideaData}></idea-preview-card>
        </div>

        <div class="actions">
          <sl-button variant="default" @click=${this.handleBackToEdit}>
            <sl-icon slot="prefix" src="${arrowLeftIcon}"></sl-icon>
            Back to Edit
          </sl-button>
          
          <sl-button variant="primary" @click=${this.handleSubmitIdea}>
            <sl-icon slot="prefix" src="${rocketIcon}"></sl-icon>
            Submit to Blockchain
          </sl-button>
        </div>
      </div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'preview-idea': PreviewIdea;
  }
}
