import { LitElement, css } from 'lit';
import { html, SignalWatcher } from '@lit-labs/signals';
import { customElement, property } from 'lit/decorators.js';

import '@components/common/vertical-fade';

import { largeCardStyles } from '@styles/large-card-styles';
import { formattedText } from '@utils/format-utils';

export interface IdeaPreviewData {
  name?: string;
  description?: string;
  tags?: string[];
}

@customElement('idea-preview-card')
export class IdeaPreviewCard extends SignalWatcher(LitElement) {
  static styles = [
    largeCardStyles,
    css`
      .preview-card {
        border: 2px dashed var(--accent);
        border-radius: 8px;
        background: var(--card-background);
        position: relative;
      }

      .preview-badge {
        position: absolute;
        top: -12px;
        left: 16px;
        background: var(--accent);
        color: var(--sl-color-neutral-0);
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 1rem;
      }

      .tag {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        background-color: var(--subtle-background);
        border-radius: 1rem;
        font-size: 0.875rem;
        color: var(--main-foreground);
      }

      .placeholder {
        color: var(--no-results);
        font-style: italic;
      }

      .preview-info {
        color: var(--subtle-text);
        font-size: 0.9rem;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: var(--subtle-background);
        border-radius: 6px;
      }
    `,
  ];

  @property({ type: Object }) ideaData!: IdeaPreviewData;

  render() {
    const { name, description, tags } = this.ideaData;

    return html`
      <div class="preview-card card">
        <div class="preview-badge">Preview</div>
        
        <div class="preview-info">
          This is how your idea will appear to others once submitted to the blockchain.
        </div>

        <div class="card-header">
          <h3 class="entity-name">
            ${name || html`<span class="placeholder">Your idea name will appear here</span>`}
          </h3>
          <div class="byline">
            <span class="placeholder">by You</span>
          </div>
        </div>

        <ul class="info-row">
          <li>🌱 <span class="placeholder">Just now</span></li>
          <li>
            🎁 <span class="placeholder">Funder reward will be shown here</span>
          </li>
          <li>
            🔥 <span class="placeholder">Interest will grow over time</span>
          </li>
        </ul>

        ${description
          ? html`
              <vertical-fade class="description">
                ${formattedText(description)}
              </vertical-fade>
            `
          : html`
              <div class="description placeholder">
                Your idea description will appear here. You can use markdown formatting 
                like **bold text**, *italic text*, and [links](https://example.com).
              </div>
            `}

        ${tags && tags.length > 0
          ? html`
              <div class="tags">
                ${tags.map((tag) => html`<span class="tag">${tag}</span>`)}
              </div>
            `
          : html`
              <div class="tags">
                <span class="tag placeholder">your-tags</span>
                <span class="tag placeholder">will-appear</span>
                <span class="tag placeholder">here</span>
              </div>
            `}
      </div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'idea-preview-card': IdeaPreviewCard;
  }
}
